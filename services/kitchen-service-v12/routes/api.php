<?php

use App\Http\Controllers\Api\CustomerIntegrationController;
use App\Http\Controllers\Api\DeliveryIntegrationController;
use App\Http\Controllers\Api\HealthController;
use App\Http\Controllers\Api\KitchenController;
use App\Http\Controllers\Api\KitchenMasterController;
use App\Http\Controllers\Api\KitchenPreparationController;
use App\Http\Controllers\Api\RecipeController;
use App\Http\Controllers\Api\V2\HealthController as V2HealthController;
use App\Http\Controllers\Api\V2\MetricsController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('/health', [HealthController::class, 'index']);

// API v1 routes (for backward compatibility)
Route::prefix('v1')->group(function () {
    // Kitchen routes
    Route::get('/kitchens', [KitchenController::class, 'index']);
    Route::get('/kitchens/{id}', [KitchenController::class, 'show']);
    Route::post('/kitchens/{id}/prepared', [KitchenController::class, 'updatePrepared']);
    Route::post('/kitchens/{id}/prepared/all', [KitchenController::class, 'updateAllPrepared']);

    // Recipe routes
    Route::get('/recipes/{id}', [RecipeController::class, 'show']);

    // Kitchen master routes
    Route::apiResource('/kitchen-masters', KitchenMasterController::class);
});

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    // Health Check Routes
    Route::get('/kitchen/health', [V2HealthController::class, 'check']);
    Route::get('/kitchen/health/detailed', [V2HealthController::class, 'check'])
        ->middleware(['auth:sanctum', 'can:admin']);

    // Metrics Route
    Route::get('/kitchen/metrics', [MetricsController::class, 'export'])
        ->middleware(['auth:sanctum', 'can:admin']);

    // Public routes
    Route::get('/recipes/{id}', [RecipeController::class, 'show']);

    // Integration routes for other services (with service authentication)
    Route::prefix('integration')->group(function () {
        // Preparation status endpoints for QuickServe
        Route::get('/preparation-status', [KitchenPreparationController::class, 'getPreparationStatus']);
        Route::get('/orders/{orderId}/preparation-status', [KitchenPreparationController::class, 'getOrderPreparationStatus']);
        Route::get('/preparation-summary', [KitchenPreparationController::class, 'getPreparationSummary']);

        // Delivery integration endpoints
        Route::get('/delivery/orders/{orderId}/preparation-status', [DeliveryIntegrationController::class, 'getOrderPreparationStatus']);
        Route::get('/delivery/orders/{orderId}/estimate-delivery-time', [DeliveryIntegrationController::class, 'estimateDeliveryTime']);
        Route::post('/delivery/status-update', [DeliveryIntegrationController::class, 'notifyDeliveryStatusUpdate']);

        // Customer integration endpoints
        Route::get('/customer/orders/{orderId}/preparation-status', [CustomerIntegrationController::class, 'getOrderPreparationStatus']);
        Route::post('/customer/orders/preparation-status', [CustomerIntegrationController::class, 'getMultipleOrdersPreparationStatus']);
        Route::get('/customer/{customerId}/preparation-summary', [CustomerIntegrationController::class, 'getCustomerPreparationSummary']);
    });

    // Direct kitchen routes (for frontend integration)
    Route::prefix('kitchens')->middleware(['auth:sanctum'])->group(function () {
        // Core kitchen operations
        Route::get('/', [KitchenController::class, 'index']);
        Route::get('/{id}', [KitchenController::class, 'show']);
        Route::post('/{id}/prepared', [KitchenController::class, 'updatePrepared']);
        Route::post('/{id}/prepared/all', [KitchenController::class, 'updateAllPrepared']);

        // Kitchen order management
        Route::get('/orders', [KitchenController::class, 'getOrders']);
        Route::get('/orders/{orderId}', [KitchenController::class, 'getOrder']);
        Route::post('/orders/{orderId}/start', [KitchenController::class, 'startOrderPreparation']);
        Route::post('/orders/{orderId}/ready', [KitchenController::class, 'markOrderReady']);
        Route::post('/orders/{orderId}/complete', [KitchenController::class, 'markOrderComplete']);
        Route::get('/orders/{orderId}/status', [KitchenController::class, 'getOrderStatus']);
        Route::post('/orders/{orderId}/notes', [KitchenController::class, 'addOrderNote']);
        Route::get('/orders/{orderId}/notes', [KitchenController::class, 'getOrderNotes']);

        // Kitchen preparation tracking
        Route::get('/preparation/status', [KitchenPreparationController::class, 'getPreparationStatus']);
        Route::get('/preparation/summary', [KitchenPreparationController::class, 'getPreparationSummary']);
        Route::get('/orders/{orderId}/preparation', [KitchenPreparationController::class, 'getOrderPreparationStatus']);

        // Kitchen analytics
        Route::get('/analytics/performance', [KitchenController::class, 'getPerformanceAnalytics']);
        Route::get('/analytics/orders', [KitchenController::class, 'getOrderAnalytics']);
        Route::get('/analytics/preparation-times', [KitchenController::class, 'getPreparationTimeAnalytics']);

        // Kitchen staff management
        Route::get('/staff', [KitchenController::class, 'getStaff']);
        Route::get('/staff/{staffId}/performance', [KitchenController::class, 'getStaffPerformance']);

        // Recipe management
        Route::get('/recipes', [RecipeController::class, 'index']);
        Route::get('/recipes/{id}', [RecipeController::class, 'show']);
        Route::post('/recipes', [RecipeController::class, 'store']);
        Route::put('/recipes/{id}', [RecipeController::class, 'update']);
        Route::delete('/recipes/{id}', [RecipeController::class, 'destroy']);
    });

    // Protected routes
    Route::middleware('auth.kitchen')->group(function () {
        // Kitchen routes - accessible to all kitchen staff
        Route::get('/kitchens', [KitchenController::class, 'index']);
        Route::get('/kitchens/{id}', [KitchenController::class, 'show']);

        // Kitchen preparation routes - requires kitchen.prepare permission
        Route::middleware('kitchen.permission:kitchen.prepare')->group(function () {
            Route::post('/kitchens/{id}/prepared', [KitchenController::class, 'updatePrepared']);
            Route::post('/kitchens/{id}/prepared/all', [KitchenController::class, 'updateAllPrepared']);
        });

        // Kitchen master routes - requires kitchen manager role or kitchen.master.manage permission
        Route::middleware(['kitchen.role:kitchen_manager,head_chef,admin', 'kitchen.permission:kitchen.master.manage'])->group(function () {
            Route::apiResource('/kitchen-masters', KitchenMasterController::class);
        });
    });
});
