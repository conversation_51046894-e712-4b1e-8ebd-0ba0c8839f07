<?php

namespace App\Services;

use App\Events\OrderPrepared;
use App\Events\AllOrdersPrepared;
use App\Models\Kitchen;
use App\Models\KitchenMaster;
use App\Models\Product;
use App\Models\UserKitchen;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class KitchenService
{
    /**
     * Get all kitchens with optional filtering.
     *
     * @param array $filters
     * @return Collection
     */
    public function getAllKitchens(array $filters = []): Collection
    {
        $query = Kitchen::with(['product', 'kitchenMaster']);

        if (isset($filters['date'])) {
            $query->byDate($filters['date']);
        } else {
            $query->byDate(date('Y-m-d'));
        }

        if (isset($filters['menu'])) {
            $query->byMenu($filters['menu']);
        }

        if (isset($filters['kitchen_id'])) {
            $query->byKitchen($filters['kitchen_id']);
        }

        return $query->get();
    }

    /**
     * Get kitchen by ID.
     *
     * @param int $id
     * @return Kitchen|null
     */
    public function getKitchenById(int $id): ?Kitchen
    {
        return Kitchen::with(['product', 'kitchenMaster'])->find($id);
    }

    /**
     * Update prepared count for a kitchen.
     *
     * @param int $id
     * @param array $data
     * @return Kitchen|null
     */
    public function updatePrepared(int $id, array $data): ?Kitchen
    {
        try {
            DB::beginTransaction();

            $kitchen = Kitchen::find($id);

            if (!$kitchen) {
                throw new \Exception('Kitchen not found');
            }

            $remainingOrders = $kitchen->total_order - $kitchen->prepared;

            if ($remainingOrders <= 0) {
                throw new \Exception('No new orders to prepare');
            }

            $kitchen->prepared += 1;
            $kitchen->save();

            // Dispatch event
            event(new OrderPrepared($kitchen));

            // Check if all orders are prepared
            if ($kitchen->prepared >= $kitchen->total_order) {
                event(new AllOrdersPrepared($kitchen));
            }

            DB::commit();

            return $kitchen->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('kitchen')->error('Error updating prepared count: ' . $e->getMessage(), [
                'kitchen_id' => $id,
                'data' => $data,
                'exception' => $e
            ]);

            return null;
        }
    }

    /**
     * Update all prepared count for a kitchen.
     *
     * @param int $id
     * @param array $data
     * @return Kitchen|null
     */
    public function updateAllPrepared(int $id, array $data): ?Kitchen
    {
        try {
            DB::beginTransaction();

            $kitchen = Kitchen::find($id);

            if (!$kitchen) {
                throw new \Exception('Kitchen not found');
            }

            $remainingOrders = $kitchen->total_order - $kitchen->prepared;

            if ($remainingOrders <= 0) {
                throw new \Exception('No new orders to prepare');
            }

            $kitchen->prepared = $kitchen->total_order;
            $kitchen->save();

            // Dispatch event
            event(new AllOrdersPrepared($kitchen));

            DB::commit();

            return $kitchen->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('kitchen')->error('Error updating all prepared count: ' . $e->getMessage(), [
                'kitchen_id' => $id,
                'data' => $data,
                'exception' => $e
            ]);

            return null;
        }
    }

    /**
     * Get recipe for a product.
     *
     * @param int $productId
     * @return array|null
     */
    public function getRecipe(int $productId): ?array
    {
        $product = Product::find($productId);

        if (!$product) {
            return null;
        }

        return [
            'name' => $product->name,
            'recipe' => $product->recipe
        ];
    }

    /**
     * Get all kitchen masters.
     *
     * @param array $filters
     * @return Collection
     */
    public function getAllKitchenMasters(array $filters = []): Collection
    {
        $query = KitchenMaster::query();

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['company_id'])) {
            $query->byCompany($filters['company_id']);
        }

        if (isset($filters['unit_id'])) {
            $query->byUnit($filters['unit_id']);
        }

        return $query->get();
    }

    /**
     * Get kitchen master by ID.
     *
     * @param int $id
     * @return KitchenMaster|null
     */
    public function getKitchenMasterById(int $id): ?KitchenMaster
    {
        return KitchenMaster::find($id);
    }

    /**
     * Create a new kitchen master.
     *
     * @param array $data
     * @return KitchenMaster
     */
    public function createKitchenMaster(array $data): KitchenMaster
    {
        return KitchenMaster::create($data);
    }

    /**
     * Update a kitchen master.
     *
     * @param int $id
     * @param array $data
     * @return KitchenMaster|null
     */
    public function updateKitchenMaster(int $id, array $data): ?KitchenMaster
    {
        $kitchenMaster = KitchenMaster::find($id);

        if (!$kitchenMaster) {
            return null;
        }

        $kitchenMaster->update($data);

        return $kitchenMaster->fresh();
    }

    /**
     * Delete a kitchen master.
     *
     * @param int $id
     * @return bool
     */
    public function deleteKitchenMaster(int $id): bool
    {
        $kitchenMaster = KitchenMaster::find($id);

        if (!$kitchenMaster) {
            return false;
        }

        return $kitchenMaster->delete();
    }

    /**
     * Get preparation status for products.
     *
     * @param array $productIds
     * @param int|null $kitchenId
     * @param string $date
     * @param string $menu
     * @return Collection
     */
    public function getPreparationStatus(array $productIds, ?int $kitchenId = null, string $date = null, string $menu = 'lunch'): Collection
    {
        $date = $date ?? date('Y-m-d');

        $query = Kitchen::with(['product', 'kitchenMaster'])
            ->whereIn('fk_product_code', $productIds)
            ->where('date', $date)
            ->where('order_menu', $menu);

        if ($kitchenId) {
            $query->where('fk_kitchen_code', $kitchenId);
        }

        return $query->get();
    }

    /**
     * Get preparation status for an order.
     *
     * @param string $orderId
     * @param string $date
     * @param string $menu
     * @return array
     */
    public function getOrderPreparationStatus(string $orderId, string $date = null, string $menu = 'lunch'): array
    {
        $date = $date ?? date('Y-m-d');

        // In a real implementation, we would fetch the order details from QuickServe
        // and then get the preparation status for each product in the order.
        // For now, we'll simulate this by using a HTTP client to call the QuickServe API.

        try {
            // Get order details from QuickServe
            $orderDetails = $this->getOrderDetailsFromQuickServe($orderId);

            if (empty($orderDetails) || empty($orderDetails['items'])) {
                return [
                    'is_fully_prepared' => false,
                    'preparation_percentage' => 0,
                    'items' => []
                ];
            }

            // Extract product IDs from order
            $productIds = array_column($orderDetails['items'], 'product_id');

            // Get preparation status for products
            $kitchenId = $orderDetails['kitchen_id'] ?? null;
            $preparationStatus = $this->getPreparationStatus($productIds, $kitchenId, $date, $menu);

            // Calculate overall preparation status
            $totalItems = count($orderDetails['items']);
            $preparedItems = 0;

            foreach ($preparationStatus as $status) {
                if ($status->prepared >= $status->total_order) {
                    $preparedItems++;
                }
            }

            $preparationPercentage = $totalItems > 0 ? round(($preparedItems / $totalItems) * 100, 2) : 0;

            return [
                'is_fully_prepared' => $preparedItems === $totalItems,
                'preparation_percentage' => $preparationPercentage,
                'items' => $preparationStatus
            ];
        } catch (\Exception $e) {
            Log::channel('kitchen')->error('Error getting order preparation status: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'date' => $date,
                'menu' => $menu,
                'exception' => $e
            ]);

            return [
                'is_fully_prepared' => false,
                'preparation_percentage' => 0,
                'items' => []
            ];
        }
    }

    /**
     * Get preparation summary for a date and menu.
     *
     * @param int|null $kitchenId
     * @param string $date
     * @param string $menu
     * @return array
     */
    public function getPreparationSummary(?int $kitchenId = null, string $date = null, string $menu = 'lunch'): array
    {
        $date = $date ?? date('Y-m-d');

        $query = Kitchen::where('date', $date)
            ->where('order_menu', $menu);

        if ($kitchenId) {
            $query->where('fk_kitchen_code', $kitchenId);
        }

        $kitchens = $query->get();

        $totalOrders = $kitchens->sum('total_order');
        $totalPrepared = $kitchens->sum('prepared');
        $preparationPercentage = $totalOrders > 0 ? round(($totalPrepared / $totalOrders) * 100, 2) : 0;

        return [
            'date' => $date,
            'menu' => $menu,
            'kitchen_id' => $kitchenId,
            'total_orders' => $totalOrders,
            'total_prepared' => $totalPrepared,
            'remaining' => $totalOrders - $totalPrepared,
            'preparation_percentage' => $preparationPercentage,
            'is_fully_prepared' => $totalPrepared >= $totalOrders
        ];
    }

    /**
     * Get order details from QuickServe.
     *
     * @param string $orderId
     * @return array
     */
    private function getOrderDetailsFromQuickServe(string $orderId): array
    {
        // In a real implementation, we would use a HTTP client to call the QuickServe API.
        // For now, we'll return a mock response.

        // TODO: Implement actual API call to QuickServe

        // Mock response
        return [
            'order_id' => $orderId,
            'kitchen_id' => 1,
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2
                ],
                [
                    'product_id' => 2,
                    'quantity' => 1
                ]
            ]
        ];
    }
}
