FROM php:8.3-fpm

# Set working directory
WORKDIR /var/www/html

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    locales \
    zip \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    git \
    curl \
    libzip-dev \
    libonig-dev \
    libicu-dev

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install extensions
RUN docker-php-ext-install pdo_mysql mbstring zip exif pcntl bcmath opcache intl
RUN docker-php-ext-configure gd --with-freetype --with-jpeg
RUN docker-php-ext-install gd

# Install composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy application files
COPY . /var/www/html
#COPY ../../packages/resilience /var/www/html/packages/resilience
#RUN composer update cubeonebiz/resilience
# Copy custom php.ini
#COPY docker/php/php.ini /usr/local/etc/php/conf.d/custom.ini

# Install dependencies
RUN composer update --no-interaction --optimize-autoloader --no-dev
#RUN composer install --no-interaction --optimize-autoloader --no-dev
#RUN composer install --no-interaction --optimize-autoloader
#RUN php artisan migrate
#RUN php artisan optimize:clear
#RUN php artisan optimize

# Generate application key
#RUN php artisan key:generate

# Expose port 9000 for PHP-FPM
EXPOSE 9006

# Start PHP-FPM
CMD ["php", "artisan", "serve", "--host", "0.0.0.0", "--port", "9006"]


