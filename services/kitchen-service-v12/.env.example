APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/
RABBITMQ_EXCHANGE=fooddialer
RABBITMQ_EXCHANGE_TYPE=topic
RABBITMQ_KITCHEN_QUEUE=kitchen_events
RABBITMQ_ORDER_QUEUE=order_events
RABBITMQ_DELIVERY_QUEUE=delivery_events
RABBITMQ_CUSTOMER_QUEUE=customer_events
RABBITMQ_QUICKSERVE_QUEUE=quickserve_events
RABBITMQ_DEAD_LETTER_EXCHANGE=fooddialer.dlx
RABBITMQ_DEAD_LETTER_QUEUE=kitchen_dlq
RABBITMQ_CONSUMER_PREFETCH_COUNT=10
RABBITMQ_CONSUMER_TIMEOUT=3
RABBITMQ_CONSUMER_SLEEP_ON_ERROR=5
RABBITMQ_CONSUMER_MAX_RETRIES=3

# Service URLs
AUTH_SERVICE_URL=http://auth-service-v12:8000
QUICKSERVE_SERVICE_URL=http://quickserve-service-v12:8000
DELIVERY_SERVICE_URL=http://delivery-service-v12:8000
CUSTOMER_SERVICE_URL=http://customer-service-v12:8000

# Circuit Breaker Configuration
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=30
